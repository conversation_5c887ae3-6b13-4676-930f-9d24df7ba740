<template>
  <div ref="threeContainer" class="nebula-container">
    <!-- 控制面板 -->
    <div class="control-panel" v-if="showControls">
      <div class="level-info">
        <span>当前层级: {{ currentLevel }}</span>
        <span>深度: {{ stackDepth }}</span>
      </div>
      <button
        @click="goBack"
        :disabled="currentLevel <= 0 || isTransitioning"
        class="back-button"
      >
        返回上级
      </button>
      <button @click="toggleControls" class="toggle-button">
        {{ showControls ? "隐藏" : "显示" }}控制
      </button>
    </div>

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-indicator">
      <div class="spinner"></div>
      <span>正在加载星云...</span>
    </div>

    <!-- 过渡指示器 -->
    <div v-if="isTransitioning" class="transition-indicator">
      <div class="transition-progress"></div>
      <span>正在进入星云...</span>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, onUnmounted } from "vue";
import useThree from "../hooks/useThree";
import NebulaManager from "./areas/nebulaManager";

const threeContainer = ref(null);
const { scene, renderer, camera, controls, performanceOptimizer } = useThree();

// 响应式状态
const currentLevel = ref(0);
const stackDepth = ref(0);
const isLoading = ref(true);
const isTransitioning = ref(false);
const showControls = ref(true);

// 星云管理器实例
let nebulaManager = null;

function animate() {
  // 更新控制器
  controls.update();

  // 更新性能统计
  performanceOptimizer.updateFrame();

  // 渲染场景
  renderer.render(scene, camera);
  requestAnimationFrame(animate);
}

async function init() {
  try {
    threeContainer.value.appendChild(renderer.domElement);

    // 创建星云管理器
    nebulaManager = new NebulaManager({
      scene,
      camera,
      element: threeContainer.value,
      onLevelChange: (level, areas) => {
        currentLevel.value = level;
        stackDepth.value = nebulaManager.getStackDepth();
        console.log(`切换到层级 ${level}`, areas);
      },
      onTransitionStart: (fromLevel, toLevel) => {
        isTransitioning.value = true;
        console.log(`开始从层级 ${fromLevel} 切换到层级 ${toLevel}`);
      },
      onTransitionComplete: (level, areas) => {
        isTransitioning.value = false;
        console.log(`完成切换到层级 ${level}`, areas);
      },
    });

    // 初始化根级星云
    await nebulaManager.initRootLevel();

    isLoading.value = false;
    animate();
  } catch (error) {
    console.error("初始化失败:", error);
    isLoading.value = false;
  }
}

// 返回上级
async function goBack() {
  if (nebulaManager) {
    const success = await nebulaManager.goBack();
    if (!success) {
      console.warn("无法返回上级");
    }
  }
}

// 切换控制面板显示
function toggleControls() {
  showControls.value = !showControls.value;
}

// 键盘事件处理
function handleKeydown(event) {
  switch (event.key) {
    case "Escape":
    case "Backspace":
      goBack();
      break;
    case "h":
    case "H":
      toggleControls();
      break;
  }
}

onMounted(() => {
  init();
  window.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  // 清理资源
  if (nebulaManager) {
    nebulaManager.dispose();
  }
  if (performanceOptimizer) {
    performanceOptimizer.dispose();
  }
  window.removeEventListener("keydown", handleKeydown);
  window.removeEventListener("resize", () => {});
});
</script>

<style scoped>
.nebula-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: #000;
}

/* 控制面板样式 */
.control-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
  color: white;
  font-family: "Arial", sans-serif;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.control-panel:hover {
  background: rgba(0, 0, 0, 0.9);
  border-color: rgba(255, 255, 255, 0.4);
}

.level-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
  font-size: 14px;
}

.level-info span {
  color: #00ffff;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.back-button,
.toggle-button {
  background: linear-gradient(45deg, #1e3c72, #2a5298);
  border: none;
  color: white;
  padding: 8px 16px;
  margin: 4px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.back-button:hover:not(:disabled),
.toggle-button:hover {
  background: linear-gradient(45deg, #2a5298, #1e3c72);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.back-button:disabled {
  background: #333;
  cursor: not-allowed;
  opacity: 0.5;
}

/* 加载指示器样式 */
.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  color: white;
  font-size: 18px;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
}

.spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 255, 255, 0.3);
  border-top: 3px solid #00ffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 过渡指示器样式 */
.transition-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  color: white;
  font-size: 16px;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

.transition-progress {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.transition-progress::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, #00ffff, transparent);
  animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-panel {
    top: 10px;
    left: 10px;
    padding: 10px;
    font-size: 12px;
  }

  .back-button,
  .toggle-button {
    padding: 6px 12px;
    font-size: 10px;
  }

  .loading-indicator {
    font-size: 16px;
  }

  .transition-indicator {
    font-size: 14px;
  }

  .transition-progress {
    width: 150px;
  }
}

/* 性能优化 - 减少重绘 */
.nebula-container * {
  will-change: transform, opacity;
}
</style>
