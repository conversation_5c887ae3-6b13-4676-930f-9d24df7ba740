import initCloud from "./cloud";
import generateStars from "./stars";
import generateCenterLight from "./center";
import { computeAreasPos } from "./utils";
import * as THREE from "three";
import { createClickEvent, bindClick } from "./utils";
import { LDrawUtils } from "three/examples/jsm/utils/LDrawUtils.js";

/**
 *创建一个邻域集合
 */
export default async function generateArea(options) {
  const { ele, camera, scene } = options;
  const generateCloud = await initCloud();
  const originArea = createArea(generateCloud);

  const areasGroup = new THREE.Group();
  const count = 10;
  const posArr = computeAreasPos({
    count, //邻域数量
    z: 0, // 层级y值
    baseRadius: 5, // 基础半径
    radiusStep: 2, // 半径步长
    angleOffsetStep: Math.PI / 10, // 每圈错开18度
  });
  posArr.forEach((pos) => {
    const area = originArea.clone();
    area.position.set(pos[0], pos[1], pos[2]);
    areasGroup.add(area);
  });

  //创建点击事件
  const { clear, event, updateSize } = createClickEvent({
    ele,
    scene: areasGroup,
    camera,
    clickCallback: (nebulaG) => {
      // 如果星云有自定义的点击处理器，则调用它
      if (nebulaG.userData && nebulaG.userData.clickHandler) {
        nebulaG.userData.clickHandler(nebulaG);
      } else {
        // 默认行为：获取世界坐标并打印
        const worldPos = nebulaG.position.clone();
        worldPos.applyMatrix4(nebulaG.matrixWorld);
        console.log("Clicked nebula at:", worldPos);
      }
    },
  });
  ele.addEventListener("click", event);

  return areasGroup;
}

/**
 * 创建一个邻域
 */
export function createArea(generateCloud) {
  const area = new THREE.Group();
  const cloud = generateCloud(); //nebula
  const { stars, update } = generateStars(); //star
  const centerLights = generateCenterLight(); //center light
  // const margGroup = new THREE.Group();
  // margGroup.add(cloud, centerLights);

  // area.add(bindClick(LDrawUtils.mergeObject(margGroup)));
  area.add(bindClick(cloud), centerLights, stars);
  // area.add(stars);

  return area;
}
