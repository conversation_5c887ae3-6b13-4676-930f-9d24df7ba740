# 星云切换动画系统

## 功能概述

这个系统实现了一个高性能的多层级星云切换动画，具有以下特性：

### 核心功能
1. **多层级星云系统** - 支持无限层级的星云嵌套
2. **丝滑切换动画** - 相机位置平滑过渡，星云淡入淡出效果
3. **几何计算精确** - 确保新星云集合在相机-点击星云的延长线上
4. **性能优化** - 自适应质量调整，LOD系统，视锥体剔除

### 使用方法

#### 基本使用
```javascript
import NebulaManager from './areas/nebulaManager.js';

const nebulaManager = new NebulaManager({
  scene,
  camera,
  element: containerElement,
  onLevelChange: (level, areas) => {
    console.log(`切换到层级 ${level}`);
  }
});

// 初始化根级星云
await nebulaManager.initRootLevel();
```

#### 交互控制
- **鼠标点击** - 点击任意星云进入下一层级
- **ESC/Backspace键** - 返回上一层级
- **H键** - 切换控制面板显示

### 技术特性

#### 动画系统
- 使用 `requestAnimationFrame` 确保流畅的60fps动画
- 三次贝塞尔缓动函数提供自然的动画曲线
- 分离的淡入淡出时序，避免视觉冲突

#### 性能优化
- **自适应质量** - 根据帧率自动调整渲染质量
- **LOD系统** - 根据层级自动调整星云数量和大小
- **视锥体剔除** - 只渲染可见的星云对象
- **内存管理** - 自动清理不再使用的资源

#### 几何计算
```javascript
// 计算新星云集合位置的核心算法
const direction = targetAreaPos.clone().sub(cameraPos).normalize();
const newCenterPos = targetAreaPos.clone().add(direction.multiplyScalar(distance));
```

### 配置选项

#### NebulaManager 配置
```javascript
{
  maxLevels: 5,           // 最大层级数
  enableLOD: true,        // 启用LOD优化
  onLevelChange: fn,      // 层级变化回调
  onTransitionStart: fn,  // 动画开始回调
  onTransitionComplete: fn // 动画完成回调
}
```

#### 动画配置
```javascript
{
  duration: 2000,         // 动画持续时间(毫秒)
  fadeOutSpeed: 1.5,      // 淡出速度倍数
  fadeInDelay: 0.3,       // 淡入延迟比例
  distance: 15            // 新星云集合距离
}
```

### 性能监控

系统提供实时性能监控：
- FPS监控
- 绘制调用数统计
- 三角形数量统计
- 自动性能优化触发

### 浏览器兼容性

- **现代浏览器** - Chrome 80+, Firefox 75+, Safari 13+
- **WebGL支持** - 需要WebGL 1.0或更高版本
- **移动设备** - 支持iOS Safari, Chrome Mobile

### 故障排除

#### 常见问题
1. **动画卡顿** - 检查设备性能，系统会自动降低质量
2. **点击无响应** - 确保星云对象有正确的点击标记
3. **内存泄漏** - 确保在组件销毁时调用 `dispose()` 方法

#### 调试模式
```javascript
// 启用调试日志
console.log('Performance stats:', performanceOptimizer.getStats());
```

### 扩展开发

#### 自定义动画
可以通过修改 `utils.js` 中的缓动函数来自定义动画效果：

```javascript
export function customEasing(t) {
  // 自定义缓动函数
  return t * t * (3 - 2 * t);
}
```

#### 自定义LOD策略
在 `nebulaManager.js` 中的 `applyLOD` 方法可以自定义LOD策略。

### 更新日志

#### v1.0.0
- 初始版本
- 基础多层级星云系统
- 相机动画和星云淡入淡出
- 性能优化系统
- 响应式UI控制面板
