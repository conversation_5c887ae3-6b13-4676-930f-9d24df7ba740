import generateArea from "./index.js";
import { createNebulaTransition, setAreasOpacity } from "./utils.js";
import * as THREE from "three";

/**
 * 星云层级管理器
 * 负责管理多层级星云系统和切换动画
 */
export class NebulaManager {
  constructor(options) {
    this.scene = options.scene;
    this.camera = options.camera;
    this.element = options.element;
    
    // 星云层级栈
    this.nebulaStack = [];
    this.currentLevel = 0;
    
    // 动画控制器
    this.transitionController = createNebulaTransition();
    
    // 性能优化配置
    this.maxLevels = 5; // 最大层级数
    this.enableLOD = true; // 启用LOD优化
    
    // 事件回调
    this.onLevelChange = options.onLevelChange || (() => {});
    this.onTransitionStart = options.onTransitionStart || (() => {});
    this.onTransitionComplete = options.onTransitionComplete || (() => {});
  }

  /**
   * 初始化根级星云
   */
  async initRootLevel() {
    try {
      const rootAreas = await generateArea({
        ele: this.element,
        camera: this.camera,
        scene: this.scene,
      });
      
      // 设置根级星云的点击事件
      this.setupClickEvents(rootAreas, 0);
      
      this.scene.add(rootAreas);
      this.nebulaStack.push({
        level: 0,
        areas: rootAreas,
        isActive: true
      });
      
      this.onLevelChange(0, rootAreas);
      return rootAreas;
    } catch (error) {
      console.error('Failed to initialize root level:', error);
      throw error;
    }
  }

  /**
   * 设置星云集合的点击事件
   * @param {THREE.Group} areasGroup 星云集合
   * @param {number} level 层级
   */
  setupClickEvents(areasGroup, level) {
    // 为每个星云添加点击事件监听
    areasGroup.children.forEach((area, index) => {
      area.userData.level = level;
      area.userData.index = index;
      area.userData.clickHandler = (clickedArea) => {
        this.handleNebulaClick(clickedArea);
      };
    });
  }

  /**
   * 处理星云点击事件
   * @param {THREE.Object3D} clickedNebula 被点击的星云
   */
  async handleNebulaClick(clickedNebula) {
    // 防止动画期间的重复点击
    if (this.transitionController.isTransitioning()) {
      return;
    }

    const currentLevel = this.currentLevel;
    const nextLevel = currentLevel + 1;
    
    // 检查是否超过最大层级
    if (nextLevel >= this.maxLevels) {
      console.warn('Maximum nesting level reached');
      return;
    }

    try {
      this.onTransitionStart(currentLevel, nextLevel);
      
      // 获取点击星云的世界坐标
      const worldPos = new THREE.Vector3();
      clickedNebula.getWorldPosition(worldPos);
      
      // 创建新的星云集合
      const newAreas = await this.createSubLevel(worldPos, nextLevel);
      
      // 获取当前活跃的星云集合
      const currentAreas = this.getCurrentAreas();
      
      // 执行切换动画
      const success = this.transitionController.startTransition({
        camera: this.camera,
        currentAreas: currentAreas,
        targetAreas: newAreas,
        targetPosition: worldPos,
        scene: this.scene,
        duration: 2000,
        onComplete: (newAreasGroup) => {
          this.onTransitionComplete(nextLevel, newAreasGroup);
          this.currentLevel = nextLevel;
          this.onLevelChange(nextLevel, newAreasGroup);
        }
      });

      if (success) {
        // 添加到栈中
        this.nebulaStack.push({
          level: nextLevel,
          areas: newAreas,
          isActive: true
        });
        
        // 标记当前层级为非活跃
        if (this.nebulaStack[currentLevel]) {
          this.nebulaStack[currentLevel].isActive = false;
        }
      }
      
    } catch (error) {
      console.error('Failed to handle nebula click:', error);
    }
  }

  /**
   * 创建子层级星云
   * @param {THREE.Vector3} centerPosition 中心位置
   * @param {number} level 层级
   */
  async createSubLevel(centerPosition, level) {
    const newAreas = await generateArea({
      ele: this.element,
      camera: this.camera,
      scene: this.scene,
    });
    
    // 设置新层级的点击事件
    this.setupClickEvents(newAreas, level);
    
    // 根据层级调整星云大小和数量（LOD优化）
    if (this.enableLOD) {
      this.applyLOD(newAreas, level);
    }
    
    return newAreas;
  }

  /**
   * 应用LOD（Level of Detail）优化
   * @param {THREE.Group} areasGroup 星云集合
   * @param {number} level 层级
   */
  applyLOD(areasGroup, level) {
    const scaleFactor = Math.pow(0.8, level); // 每层级缩小20%
    const opacityFactor = Math.max(0.3, 1 - level * 0.1); // 透明度递减
    
    areasGroup.scale.setScalar(scaleFactor);
    
    // 根据层级减少星云数量
    const maxChildren = Math.max(3, 10 - level * 2);
    while (areasGroup.children.length > maxChildren) {
      const removed = areasGroup.children.pop();
      areasGroup.remove(removed);
    }
    
    // 调整材质属性
    setAreasOpacity(areasGroup, opacityFactor);
  }

  /**
   * 返回上一层级
   */
  async goBack() {
    if (this.currentLevel <= 0 || this.transitionController.isTransitioning()) {
      return false;
    }

    const currentAreas = this.getCurrentAreas();
    const previousLevel = this.currentLevel - 1;
    const previousAreas = this.nebulaStack[previousLevel]?.areas;

    if (!previousAreas) {
      return false;
    }

    try {
      // 重新激活上一层级
      this.nebulaStack[previousLevel].isActive = true;
      this.scene.add(previousAreas);
      setAreasOpacity(previousAreas, 1);

      // 执行返回动画
      const success = this.transitionController.startTransition({
        camera: this.camera,
        currentAreas: currentAreas,
        targetAreas: previousAreas,
        targetPosition: this.camera.position.clone().add(new THREE.Vector3(0, 0, -10)),
        scene: this.scene,
        duration: 1500,
        onComplete: () => {
          this.currentLevel = previousLevel;
          this.nebulaStack.pop(); // 移除当前层级
          this.onLevelChange(previousLevel, previousAreas);
        }
      });

      return success;
    } catch (error) {
      console.error('Failed to go back:', error);
      return false;
    }
  }

  /**
   * 获取当前活跃的星云集合
   */
  getCurrentAreas() {
    const current = this.nebulaStack.find(item => item.isActive);
    return current ? current.areas : null;
  }

  /**
   * 清理资源
   */
  dispose() {
    // 取消正在进行的动画
    this.transitionController.cancelTransition();
    
    // 清理所有星云集合
    this.nebulaStack.forEach(item => {
      if (item.areas.parent) {
        item.areas.parent.remove(item.areas);
      }
      // 清理材质和几何体
      item.areas.traverse(child => {
        if (child.geometry) child.geometry.dispose();
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(mat => mat.dispose());
          } else {
            child.material.dispose();
          }
        }
      });
    });
    
    this.nebulaStack = [];
    this.currentLevel = 0;
  }

  /**
   * 获取当前层级信息
   */
  getCurrentLevel() {
    return this.currentLevel;
  }

  /**
   * 获取层级栈深度
   */
  getStackDepth() {
    return this.nebulaStack.length;
  }
}

export default NebulaManager;
