import * as THREE from "three";

/**
 * WebGL性能优化器
 * 专门针对星云系统的性能优化
 */
export class PerformanceOptimizer {
  constructor(renderer, scene, camera) {
    this.renderer = renderer;
    this.scene = scene;
    this.camera = camera;
    
    // 性能监控
    this.stats = {
      frameCount: 0,
      lastTime: performance.now(),
      fps: 60,
      drawCalls: 0,
      triangles: 0
    };
    
    // 优化配置
    this.config = {
      enableFrustumCulling: true,
      enableLOD: true,
      enableInstancing: true,
      maxDrawCalls: 100,
      targetFPS: 60,
      adaptiveQuality: true
    };
    
    // LOD距离配置
    this.lodDistances = [10, 25, 50, 100];
    
    this.init();
  }

  /**
   * 初始化性能优化器
   */
  init() {
    // 启用渲染器优化
    this.optimizeRenderer();
    
    // 设置性能监控
    this.setupPerformanceMonitoring();
    
    // 启用自适应质量
    if (this.config.adaptiveQuality) {
      this.enableAdaptiveQuality();
    }
  }

  /**
   * 优化渲染器设置
   */
  optimizeRenderer() {
    const renderer = this.renderer;
    
    // 启用硬件抗锯齿
    renderer.antialias = true;
    
    // 优化阴影设置
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.shadowMap.autoUpdate = false; // 手动更新阴影
    
    // 启用色调映射
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.0;
    
    // 优化渲染状态
    renderer.sortObjects = true;
    renderer.autoClear = true;
    
    // 设置像素比（移动设备优化）
    const pixelRatio = Math.min(window.devicePixelRatio, 2);
    renderer.setPixelRatio(pixelRatio);
    
    console.log('Renderer optimized with pixel ratio:', pixelRatio);
  }

  /**
   * 设置性能监控
   */
  setupPerformanceMonitoring() {
    setInterval(() => {
      this.updateStats();
    }, 1000);
  }

  /**
   * 更新性能统计
   */
  updateStats() {
    const now = performance.now();
    const deltaTime = now - this.stats.lastTime;
    
    this.stats.fps = Math.round(1000 / (deltaTime / this.stats.frameCount));
    this.stats.frameCount = 0;
    this.stats.lastTime = now;
    
    // 获取渲染信息
    const info = this.renderer.info;
    this.stats.drawCalls = info.render.calls;
    this.stats.triangles = info.render.triangles;
    
    // 如果性能下降，触发优化
    if (this.stats.fps < this.config.targetFPS * 0.8) {
      this.triggerPerformanceOptimization();
    }
  }

  /**
   * 触发性能优化
   */
  triggerPerformanceOptimization() {
    console.warn('Performance degradation detected, applying optimizations...');
    
    // 降低渲染质量
    this.reduceRenderQuality();
    
    // 增加LOD距离
    this.adjustLODDistances(1.2);
    
    // 减少粒子数量
    this.reduceParticleCount();
  }

  /**
   * 降低渲染质量
   */
  reduceRenderQuality() {
    const currentPixelRatio = this.renderer.getPixelRatio();
    if (currentPixelRatio > 1) {
      this.renderer.setPixelRatio(Math.max(1, currentPixelRatio * 0.8));
      console.log('Reduced pixel ratio to:', this.renderer.getPixelRatio());
    }
  }

  /**
   * 调整LOD距离
   */
  adjustLODDistances(factor) {
    this.lodDistances = this.lodDistances.map(distance => distance * factor);
    console.log('Adjusted LOD distances:', this.lodDistances);
  }

  /**
   * 减少粒子数量
   */
  reduceParticleCount() {
    this.scene.traverse((object) => {
      if (object.isPoints && object.geometry) {
        const geometry = object.geometry;
        const currentCount = geometry.attributes.position.count;
        const newCount = Math.floor(currentCount * 0.8);
        
        if (newCount > 100) { // 保持最小粒子数
          this.reduceGeometryVertices(geometry, newCount);
        }
      }
    });
  }

  /**
   * 减少几何体顶点数量
   */
  reduceGeometryVertices(geometry, newCount) {
    const positions = geometry.attributes.position.array;
    const colors = geometry.attributes.color?.array;
    
    const newPositions = new Float32Array(newCount * 3);
    const newColors = colors ? new Float32Array(newCount * 3) : null;
    
    // 均匀采样顶点
    const step = Math.floor(positions.length / 3 / newCount);
    for (let i = 0; i < newCount; i++) {
      const sourceIndex = i * step * 3;
      const targetIndex = i * 3;
      
      newPositions[targetIndex] = positions[sourceIndex];
      newPositions[targetIndex + 1] = positions[sourceIndex + 1];
      newPositions[targetIndex + 2] = positions[sourceIndex + 2];
      
      if (newColors && colors) {
        newColors[targetIndex] = colors[sourceIndex];
        newColors[targetIndex + 1] = colors[sourceIndex + 1];
        newColors[targetIndex + 2] = colors[sourceIndex + 2];
      }
    }
    
    geometry.setAttribute('position', new THREE.BufferAttribute(newPositions, 3));
    if (newColors) {
      geometry.setAttribute('color', new THREE.BufferAttribute(newColors, 3));
    }
    
    geometry.setDrawRange(0, newCount);
    console.log(`Reduced geometry vertices from ${positions.length / 3} to ${newCount}`);
  }

  /**
   * 启用自适应质量
   */
  enableAdaptiveQuality() {
    let lastFrameTime = performance.now();
    
    const adaptiveUpdate = () => {
      const now = performance.now();
      const frameTime = now - lastFrameTime;
      lastFrameTime = now;
      
      // 目标帧时间（60fps = 16.67ms）
      const targetFrameTime = 1000 / this.config.targetFPS;
      
      if (frameTime > targetFrameTime * 1.5) {
        // 性能不足，降低质量
        this.adaptQualityDown();
      } else if (frameTime < targetFrameTime * 0.8) {
        // 性能充足，提升质量
        this.adaptQualityUp();
      }
      
      requestAnimationFrame(adaptiveUpdate);
    };
    
    requestAnimationFrame(adaptiveUpdate);
  }

  /**
   * 降低质量
   */
  adaptQualityDown() {
    // 实现质量降低逻辑
    const currentPixelRatio = this.renderer.getPixelRatio();
    if (currentPixelRatio > 0.5) {
      this.renderer.setPixelRatio(currentPixelRatio * 0.9);
    }
  }

  /**
   * 提升质量
   */
  adaptQualityUp() {
    // 实现质量提升逻辑
    const currentPixelRatio = this.renderer.getPixelRatio();
    const maxPixelRatio = Math.min(window.devicePixelRatio, 2);
    if (currentPixelRatio < maxPixelRatio) {
      this.renderer.setPixelRatio(Math.min(maxPixelRatio, currentPixelRatio * 1.1));
    }
  }

  /**
   * 应用视锥体剔除
   */
  applyFrustumCulling(objects) {
    if (!this.config.enableFrustumCulling) return objects;
    
    const frustum = new THREE.Frustum();
    const matrix = new THREE.Matrix4().multiplyMatrices(
      this.camera.projectionMatrix,
      this.camera.matrixWorldInverse
    );
    frustum.setFromProjectionMatrix(matrix);
    
    return objects.filter(object => {
      const sphere = new THREE.Sphere();
      object.geometry?.computeBoundingSphere();
      if (object.geometry?.boundingSphere) {
        sphere.copy(object.geometry.boundingSphere);
        sphere.applyMatrix4(object.matrixWorld);
        return frustum.intersectsSphere(sphere);
      }
      return true;
    });
  }

  /**
   * 获取性能统计信息
   */
  getStats() {
    return { ...this.stats };
  }

  /**
   * 更新帧计数
   */
  updateFrame() {
    this.stats.frameCount++;
  }

  /**
   * 销毁优化器
   */
  dispose() {
    // 清理资源
    console.log('Performance optimizer disposed');
  }
}

export default PerformanceOptimizer;
