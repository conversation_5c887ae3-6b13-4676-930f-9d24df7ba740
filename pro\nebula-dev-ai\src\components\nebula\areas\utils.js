import { CLICK_FLAG } from "./constant";
import { Raycaster, Vector2 } from "three";
/**
 * 计算单层邻域坐标位置
 * @param {object} option 参数
 */
export function computeAreasPos(
  option = {
    count: 10, //邻域数量
    z: 0, // 层级y值
    baseRadius: 5, // 基础半径
    radiusStep: 2, // 半径步长
    angleOffsetStep: Math.PI / 10, // 每圈错开18度
  }
) {
  const { count, z, baseRadius, radiusStep, angleOffsetStep } = option;
  const posArr = [];
  let y = z;
  // for (let i = 0; i < count; i++) {
  //   const r = baseRadius + i * radiusStep; // 当前层半径
  //   const angleOffset = i * angleOffsetStep; // 当前层旋转角度

  //   for (let j = 0; j < 5; j++) {
  //     // 五角星跳跃144度
  //     const angle = angleOffset + j * ((2 * Math.PI * 2) / 5); // = j * 144°
  //     const x = r * Math.cos(angle);
  //     const y = r * Math.sin(angle);
  //     posArr.push([x, z, y]);
  //   }
  // }

  const interval = 3;
  let index = 0;
  for (let round = 1, s = count; s != 0; ++round) {
    let num = interval * round;
    if (num < s) {
      s -= num;
    } else {
      num = s;
      s = 0;
    }
    //重新计算y值
    y += Math.sin(round) * 0.5;
    const r = round * 3;
    for (let i = 0; i < num; ++i) {
      const x = r * Math.cos((2 * Math.PI * i) / num);
      const z = r * Math.sin((2 * Math.PI * i) / num);
      if (index < count) {
        posArr.push([x, y, z]);
      }
    }
  }

  return posArr;
}

let index = 1;
/**
 *给星云绑定允许点击标记
 * @param {THREE.Group} areaGroup 星云组
 */
export function bindClick(areaGroup) {
  //允许自定userDta
  areaGroup.userData[CLICK_FLAG] = true;
  areaGroup.userData.name = "area-" + index++;
  return areaGroup;
}

/**
 * 点击事件
 */
export function createClickEvent(options) {
  const { ele, scene, clickCallback, camera } = options;
  const { width, height } = ele.getBoundingClientRect();
  let raycaster = new Raycaster();

  //清空闭包造成的内存泄漏
  function clear() {
    width = null;
    height = null;
    raycaster = null;
  }
  // 更新尺寸
  function updateSize(w, h) {
    width = w;
    height = h;
  }
  //生成的event事件
  function event(e) {
    const { offsetX, clientX, offsetY, clientY, button } = e;
    raycaster.setFromCamera(
      new Vector2((offsetX / width) * 2 - 1, -(offsetY / height) * 2 + 1),
      camera
    );
    const intersects = raycaster.intersectObject(scene, true); // 递归检测所有子对象

    if (intersects.length > 0) {
      // 查找第一个具有点击标记的对象
      for (let i = 0; i < intersects.length; i++) {
        const intersect = intersects[i];
        let currentObject = intersect.object;

        // 向上遍历对象层次结构，查找具有点击标记的对象
        while (currentObject) {
          if (currentObject.userData && currentObject.userData[CLICK_FLAG]) {
            // 找到了具有点击标记的对象，触发回调
            clickCallback(currentObject);
            return; // 找到第一个有效对象后立即返回
          }
          currentObject = currentObject.parent;
        }
      }
    }
  }
  return {
    clear,
    event,
    updateSize,
  };
}

/**
 * 通过相机位置和目标星云定位子层星云集合位置
 * @param {THREE.Vector3} cameraPos 相机位置
 * @param {THREE.Vector3} targetAreaPos 目标星云位置
 * @param {number} distance 新星云集合距离目标星云的距离
 * @returns {THREE.Vector3} 新星云集合的中心位置
 */
export function computeSubAreasPos(cameraPos, targetAreaPos, distance = 15) {
  // 计算从相机到目标星云的方向向量
  const direction = targetAreaPos.clone().sub(cameraPos).normalize();

  // 在这条直线上，目标星云后方distance距离处放置新的星云集合
  const newCenterPos = targetAreaPos
    .clone()
    .add(direction.multiplyScalar(distance));

  return newCenterPos;
}

/**
 * 创建星云切换动画控制器
 */
export function createNebulaTransition() {
  let isTransitioning = false;
  let animationId = null;
  let onCompleteCallback = null;

  /**
   * 执行星云切换动画
   * @param {Object} options 动画参数
   * @param {THREE.Camera} options.camera 相机对象
   * @param {THREE.Group} options.currentAreas 当前星云集合
   * @param {THREE.Group} options.targetAreas 目标星云集合
   * @param {THREE.Vector3} options.targetPosition 点击的星云位置
   * @param {THREE.Scene} options.scene 场景对象
   * @param {Function} options.onComplete 动画完成回调
   * @param {number} options.duration 动画持续时间(毫秒)
   */
  function startTransition(options) {
    if (isTransitioning) return false;

    const {
      camera,
      currentAreas,
      targetAreas,
      targetPosition,
      scene,
      onComplete,
      duration = 2000,
    } = options;

    isTransitioning = true;
    onCompleteCallback = onComplete;

    // 保存初始状态
    const startCameraPos = camera.position.clone();
    const startTime = performance.now();

    // 计算新星云集合的位置
    const newAreasCenter = computeSubAreasPos(startCameraPos, targetPosition);

    // 设置目标星云集合的初始状态
    targetAreas.position.copy(newAreasCenter);
    targetAreas.visible = true;

    // 设置所有星云的初始透明度
    setAreasOpacity(targetAreas, 0);
    scene.add(targetAreas);

    // 动画循环
    function animate() {
      const currentTime = performance.now();
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // 使用缓动函数使动画更自然
      const easeProgress = easeInOutCubic(progress);

      // 相机位置插值
      camera.position.lerpVectors(startCameraPos, targetPosition, easeProgress);

      // 星云透明度动画
      const fadeOutProgress = Math.min(progress * 1.5, 1); // 稍快一些淡出
      const fadeInProgress = Math.max((progress - 0.3) / 0.7, 0); // 延迟淡入

      setAreasOpacity(currentAreas, 1 - fadeOutProgress);
      setAreasOpacity(targetAreas, fadeInProgress);

      if (progress < 1) {
        animationId = requestAnimationFrame(animate);
      } else {
        // 动画完成
        completeTransition(currentAreas, targetAreas);
      }
    }

    animationId = requestAnimationFrame(animate);
    return true;
  }

  /**
   * 完成过渡动画
   */
  function completeTransition(currentAreas, targetAreas) {
    isTransitioning = false;

    // 清理当前星云集合
    if (currentAreas.parent) {
      currentAreas.parent.remove(currentAreas);
    }

    // 确保新星云集合完全可见
    setAreasOpacity(targetAreas, 1);

    if (onCompleteCallback) {
      onCompleteCallback(targetAreas);
    }
  }

  /**
   * 取消动画
   */
  function cancelTransition() {
    if (animationId) {
      cancelAnimationFrame(animationId);
      animationId = null;
    }
    isTransitioning = false;
  }

  return {
    startTransition,
    cancelTransition,
    isTransitioning: () => isTransitioning,
  };
}

/**
 * 设置星云集合的透明度
 * @param {THREE.Group} areasGroup 星云集合
 * @param {number} opacity 透明度 (0-1)
 */
export function setAreasOpacity(areasGroup, opacity) {
  areasGroup.traverse((child) => {
    if (child.material) {
      if (Array.isArray(child.material)) {
        child.material.forEach((material) => {
          material.transparent = true;
          material.opacity = opacity;
        });
      } else {
        child.material.transparent = true;
        child.material.opacity = opacity;
      }
    }
  });
}

/**
 * 缓动函数 - 三次方缓入缓出
 * @param {number} t 进度 (0-1)
 * @returns {number} 缓动后的进度
 */
export function easeInOutCubic(t) {
  return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
}
