import {
  WebGLRenderer,
  Scene,
  PerspectiveCamera,
  TextureLoader,
  EquirectangularReflectionMapping,
  AmbientLight,
} from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js";
import PerformanceOptimizer from "../nebula/areas/performanceOptimizer.js";
import envImg from "../nebula/assets/env_c.jpg";

const textureLoader = new TextureLoader();

export default function useThree() {
  const envTexture = textureLoader.load(envImg);
  envTexture.mapping = EquirectangularReflectionMapping;

  const scene = new Scene();
  scene.background = envTexture;
  scene.environment = envTexture;
  scene.backgroundIntensity = 0.05;
  scene.backgroundBlurriness = 0.0;

  const ambientLight = new AmbientLight(0xffffff, 8);
  scene.add(ambientLight);

  // 创建相机
  const camera = new PerspectiveCamera(
    60,
    window.innerWidth / window.innerHeight,
    0.1,
    100
  );
  camera.position.set(1.9, 4, 10);

  // 创建渲染器（启用抗锯齿和优化设置）
  const renderer = new WebGLRenderer({
    antialias: true,
    alpha: false,
    powerPreference: "high-performance",
    stencil: false,
    depth: true,
  });

  // 基础渲染器设置
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
  renderer.setSize(window.innerWidth, window.innerHeight);
  renderer.outputColorSpace = "srgb";

  // 启用阴影（如果需要）
  renderer.shadowMap.enabled = true;
  renderer.shadowMap.type = "PCFSoftShadowMap";

  // 创建轨道控制器
  const controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.05;
  controls.screenSpacePanning = false;
  controls.minDistance = 1;
  controls.maxDistance = 50;
  controls.maxPolarAngle = Math.PI;

  // 创建性能优化器
  const performanceOptimizer = new PerformanceOptimizer(
    renderer,
    scene,
    camera
  );

  function resize() {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
  }

  window.addEventListener("resize", resize);

  return {
    renderer,
    scene,
    camera,
    controls,
    performanceOptimizer,
  };
}
